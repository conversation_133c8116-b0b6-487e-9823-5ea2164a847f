body {
  font-size: 1rem;
  padding-bottom: 110px;
}

@media (max-width: 768px) {
  .container-fluid {
    padding-right: 5px;
    padding-left: 5px;
  }
}

@media (min-width: 1360px) {
  .container-fluid {
    max-width: 1360px;
    margin: 0 auto;
  }
}

.bottom-buttons {
  z-index: 99;
  position: fixed;
  left: 50%;
  bottom: 20px;
  background-color: transparent;
  display: flex;
  flex-direction: row;
  transform: translateX(-50%);
}

.bottom-buttons button {
  border-radius: 50%;
  margin: 0 10px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.title {
  margin-top: 30px;
  margin-bottom: 10px;
}

table.jsoneditor-tree > tbody > tr.jsoneditor-expandable:first-child {
  display: none;
}

.modal-dialog {
  max-width: 800px;
}

.modal-body {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.task-suggestions {
  width: 100%;
  max-height: 250px;
  overflow-y: auto;
  transform: translate(0, -100%);
  top: 0;
  margin-top: -5px;
  border: 1px solid #007bff;
  z-index: 1021;
}

/*
* Sidebar
*/

.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  /* Behind the navbar */
  padding: 54px 0 0;
  /* Height of navbar */
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
}

.sidebar-sticky {
  position: relative;
  top: 0;
  height: calc(100vh - 54px);
  padding-top: 0.5rem;
  overflow-x: hidden;
  overflow-y: auto;
  /* Scrollable contents if viewport is shorter than content. */
}

@supports ((position: -webkit-sticky) or (position: sticky)) {
  .sidebar-sticky {
    position: -webkit-sticky;
    position: sticky;
  }
}

.sidebar .nav-link {
  font-size: medium;
  color: #333;
  padding: 10px;
  transition: background-color 0.3s ease;
  /* 添加过渡效果 */
}

.sidebar .nav-link:hover {
  background-color: #e0f0ff;
  /* 改变背景颜色 */
}

.sidebar .nav-link i {
  margin-right: 10px;
  margin-left: 10px;
}

.sidebar .nav-link.active {
  background-color: #007bff;
  color: white !important;
}

.sidebar-heading {
  font-size: 0.75rem;
  text-transform: uppercase;
}

/*
* Navbar
*/

.navbar-brand {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  background-color: rgba(0, 0, 0, 0.25);
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);
}

.navbar .navbar-toggler {
  right: 1rem;
}

.navbar .form-control {
  padding: 0.75rem 1rem;
  border-width: 0;
  border-radius: 0;
}

.form-control-dark {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.1);
}

.form-control-dark:focus {
  border-color: transparent;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.25);
}

.cursor-pointer {
  cursor: pointer;
}

.nav-bottom {
  position: absolute;
  bottom: 32px;
  width: 100%;
  font-size: small;
}

.position-relative:hover .position-absolute {
  display: block !important;
}

.qrcode-tutorial {
  display: none;
  left: 50%;
  transform: translateX(-50%);
  bottom: 100%;
  margin-bottom: 10px;
  z-index: 1000;
  border: 1px solid #ddd;
  background-color: #fff;
  padding: 10px;
  border-radius: 5px;
  max-width: 100%;
  text-align: center;
}

.qrcode-tutorial img {
  max-width: 100%;
  height: auto;
}
