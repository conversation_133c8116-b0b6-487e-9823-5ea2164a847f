[{"name": "__pycache__", "size": 4096, "mode": **********, "mtime": "2025-06-26T16:11:49.741515222+08:00", "linkTarget": ""}, {"name": "app", "size": 4096, "mode": **********, "mtime": "2025-06-26T11:32:10+08:00", "linkTarget": ""}, {"name": "config", "size": 4096, "mode": **********, "mtime": "2025-06-26T18:44:16.759924255+08:00", "linkTarget": ""}, {"name": "plugins", "size": 4096, "mode": **********, "mtime": "2025-06-26T16:10:51.657860325+08:00", "linkTarget": ""}, {"name": ".dockerignore", "size": 29, "mode": 420, "mtime": "2025-06-26T11:32:10+08:00", "linkTarget": ""}, {"name": ".giti<PERSON>re", "size": 51, "mode": 420, "mtime": "2025-06-26T11:32:10+08:00", "linkTarget": ""}, {"name": "Dockerfile", "size": 440, "mode": 420, "mtime": "2025-06-26T11:32:10+08:00", "linkTarget": ""}, {"name": "LICENSE", "size": 34523, "mode": 420, "mtime": "2025-06-26T11:32:10+08:00", "linkTarget": ""}, {"name": "README.md", "size": 7945, "mode": 420, "mtime": "2025-06-26T11:32:10+08:00", "linkTarget": ""}, {"name": "notify.py", "size": 41314, "mode": 420, "mtime": "2025-06-26T11:32:10+08:00", "linkTarget": ""}, {"name": "quark_auto_save.py", "size": 31177, "mode": 438, "mtime": "2025-06-26T20:20:58+08:00", "linkTarget": ""}, {"name": "quark_config.json", "size": 2608, "mode": 420, "mtime": "2025-06-26T11:32:10+08:00", "linkTarget": ""}, {"name": "requirements.txt", "size": 42, "mode": 420, "mtime": "2025-06-26T11:32:10+08:00", "linkTarget": ""}]